export default {
  langSelect: '语言切换成功',
  login: '登录',
  logout: '登出',
  hello: '你好',
  dashboard: '首页',
  username: '用户名',
  password: '密码',
  oldPassword: '旧密码',
  newPassword: '新密码',
  confirmPassword: '确认密码',
  initPassword: '初始密码',
  pleaseEnter: '请输入或选择',
  query: '查询',
  reset: '重置',
  create: '新增',
  update: '更新',
  submit: '提交',
  recovery: '恢复',
  congratulations: '恭喜',
  success: '成功',
  fail: '失败',
  sorry: '抱歉',
  readDataFail: '查询数据失败',
  operation: '操作',
  hidden: '隐藏',
  show: '显示',
  available: '可用',
  disabled: '禁用',
  edit: '编辑',
  read: '已读',
  del: '删除',
  batchRead: '批量已读',
  batchDel: '批量删除',
  allRead: '全部已读',
  allDel: '全部删除',
  permission: '权限',
  sureToDo: '确定要',
  caution: '请谨慎操作',
  irreversible: '此操作不可逆',
  confirm: '确定',
  cancel: '取消',
  noDiff: '数据没有发生变化, 请重新输入~',
  required: '不允许为空',
  createdAt: '创建时间',
  updatedAt: '更新时间',
  healthy: '健康',
  yes: '是',
  no: '否',
  any: '无限制',
  alreadyExists: '已存在',
  name: '名称',
  title: '标题',
  content: '内容',
  status: '状态',
  locked: '锁定',
  unlock: '解锁',
  sort: '排序',
  keyword: '关键字',
  desc: '说明',
  icon: '图标',
  nickname: '昵称',
  mobile: '手机',
  introduction: '简介',
  message: '消息',
  type: '类型',
  category: '分类',
  ip: 'IP',
  port: '端口',
  remark: '备注',
  refresh: '刷新',
  connect: '连接',
  key: '键',
  val: '值',
  addition: '附加参数',
  requestPage: {
    networkError: '网络异常, 请稍后再试',
    loginExpired: '登录超时, 请稍后再试',
    loginStatusExpired: '登录状态已失效',
    loginAgainOrStayHere: '登录超时, 重新登录或继续停留在当前页?',
    loginAgain: '重新登录',
    stayHere: '继续停留'
  },
  loginPage: {
    title: '系统登录',
    captcha: '验证码',
    users: ['超级管理员', '访客', '请假人']
  },
  tagsBtn: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  settingsBtn: {
    title: '系统布局配置',
    theme: '主题色',
    showTagsView: '显示 Tags-View',
    showSidebarLogo: '显示侧边栏 Logo',
    fixedHeader: '固定 Header',
    sidebarTextTheme: '侧边栏文字主题色'
  },
  navbarBtn: {
    github: '项目地址',
    docs: '文档',
    swagger: 'Api文档',
    theme: '换肤',
    size: '布局大小',
    profile: '个人中心',
    message: '消息中心',
    loki: '系统日志(登录test/123456)',
    minio: '对象存储(登录minio/minio123)'
  },
  profilePage: {
    aboutMe: '关于我',
    info: '账户信息',
    security: '账户安全',
    passwordInconsistent: '两次输入的密码不一致',
    newPasswordSmallLen: '新密码至少6个字符'
  },
  menuRoute: {
    dashboardRoot: '首页',
    dashboard: '首页',
    systemRoot: '系统管理',
    menu: '菜单管理',
    role: '角色管理',
    user: '用户管理',
    api: '接口管理',
    dict: '数据字典',
    operationLog: '操作日志',
    messagePush: '消息推送',
    machine: '机器管理',
    profile: '个人中心',
    message: '消息中心',
    fsmRoot: '状态机',
    fsm: '状态机',
    leave: '我的请假条',
    approving: '待审批列表',
    testRoot: '测试页面',
    test: '测试用例',
    uploader: '上传组件',
    uploader1: '上传示例1',
    uploader2: '上传示例2'
  },
  dashboardPage: {
    t1: '欢迎使用Gin Web',
    t2: '点击左上角展开菜单试用各项功能',
    t3: '有问题欢迎提Issue或QQ群交流',
    t4: '广告',
    t5: '科学上网联系群主',
    t6: '每月50G免费流量',
    t7: 'UCloud云服务器',
    t8: '领劵最低只要',
    t9: '每年'
  },
  menu: '菜单',
  menuPage: {
    path: 'URL路径',
    rootPath: '根路径',
    fullPath: '完整路径',
    component: '组件路径',
    redirect: '重定向地址',
    visible: '菜单可见性',
    breadcrumb: '面包屑导航',
    parentId: '上级目录',
    topPath: '顶级目录'
  },
  role: '角色',
  roleName: '角色名',
  rolePage: {
    permissionDialogTitle: '更新权限'
  },
  user: '用户',
  userPage: {
    validate: [
      '必须以字母开头, 如a12345',
      '不允许出现汉字或特殊字符, 如a+,sa、a张三',
      '手机号格式不正确',
      '用户名至少4个字符',
      '用户名至多20个字符',
      '初始密码至少6个字符',
      '新密码至少6个字符'
    ]
  },
  api: '接口',
  apiPage: {
    path: '接口路径',
    method: '请求方式',
    showRoleTips: '可避免到角色管理中添加',
    showRoleSelect: '访问授权',
    get: '获取资源',
    post: '创建资源',
    put: '创建/更新资源',
    patch: '创建/更新资源(区别于PUT, 增量更新)',
    delete: '删除资源'
  },
  dictPage: {
    dict: '字典',
    dictData: '字典数据',
    dictDialogTitle: '字典数据'
  },
  operationLog: '操作日志',
  operationLogPage: {
    apiDesc: '接口简介',
    ipLocation: 'IP所在地',
    latency: '请求耗时',
    userAgent: '浏览器信息',
    body: '请求体',
    params: '请求参数',
    resp: '响应数据',
    showDetail: '查看详情'
  },
  messagePushPage: {
    sendNow: '立即发送',
    sendMessage: '发送消息',
    toUser: '接收人',
    toRole: '接收角色',
    one2one: '私信(一对一)',
    one2more: '通知(一对多)',
    one2all: '系统(一对全部)',
    unRead: '未读',
    read: '已读',
    fromUsername: '发送人',
    lost: '消息中心已断开',
    lostReconnect: '消息中心已断开, 将在%s秒后重连',
    lostRetry: '消息中心已断开, 请在%s秒后重试',
    newMessage: '新消息',
    newMessageDetail: '有人给你发了新的消息',
    online: '用户上线',
    onlineDetail: '用户%s刚刚上线啦',
    connected: '消息中心已连接',
    cannotConnect: '消息中心无法连接'
  },
  machine: '机器',
  host: '主机',
  terminal: '终端',
  machinePage: {
    cannotConnect: '无法连接',
    version: '系统版本',
    arch: '架构',
    name: '机器名',
    memory: '内存',
    disk: '硬盘'
  },
  fsm: '状态机',
  level: '层级',
  fsmPage: {
    submitterName: '提交人名称',
    submitterEditFields: '提交人可编辑字段',
    submitterConfirm: '提交人确认',
    submitterConfirmEditFields: '提交人确认可编辑字段',
    edit: '编辑权限',
    editFields: '可编辑字段',
    users: '可审批人',
    roles: '可审批角色',
    refuse: '拒绝权限',
    validate: [
      '分类必须为数字',
      '用户或角色至少填一项'
    ]
  },
  leave: '请假',
  startTime: '开始时间',
  endTime: '结束时间',
  approveLogTrack: '审批轨迹',
  leavePage: {
    flowName: '请假流程',
    submitted: '已提交',
    approved: '已通过',
    refusedNeedResubmit: '已拒绝, 需重新提交',
    refusedNeedResubmitDesc: '请修改后重新提交~',
    cancelled: '已手动取消',
    approving: '审批中',
    approvingDesc: '请耐心等待~',
    approvedNeedConfirm: '已通过, 需确认',
    approvedNeedConfirmDesc: '请点击确认~'
  },
  approvingPage: {
    defaultOpinion: '通过',
    opinion: '审批意见',
    reason: '原因',
    approve: '通过',
    refuse: '拒绝',
    confirm: '确认',
    resubmit: '重新提交',
    submitterUser: '提交人',
    submitterRole: '提交人角色',
    prevDetail: '状态',
    logDetail: '提交明细',
    viewLogDetail: '查看明细',
    doApproval: '审批',
    toApprove: '去审批',
    toRefuse: '去拒绝'
  },
  upload: '上传',
  uploading: '上传中',
  paused: '暂停中',
  waiting: '等待中',
  decompress: '解压',
  merged: '合并',
  uploaderPage: {
    drag: '拖拽文件到此处或者手动上传',
    file: '选择文件',
    image: '选择图片',
    folder: '选择目录',
    zip: '上传ZIP',
    startDecompress: '开始解压',
    uploaded: '已上传',
    decompressed: '已解压',
    validate: [
      '请至少上传完成一个文件再解压~'
    ]
  },
  testCase: '测试用例'
}
