/* Element Variables */

// Override Element UI variables
$--color-primary: #1890ff;
$--color-success: #13ce66;
$--color-warning: #FFBA00;
$--color-danger: #ff4949;
$--color-info: #5d5d5d;
$--button-font-weight: 400;
$--color-text-regular: #1f2d3d;
$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;
$--table-border:1px solid#dfe6ec;

// Icon font path, required
$--font-path: '~element-ui/lib/theme-chalk/fonts';

// Apply overrided variables in Element UI
@import '~element-ui/packages/theme-chalk/src/index';

// The :export directive is the magic sauce for webpack
// https://mattferderer.com/use-sass-variables-in-typescript-and-javascript
:export {
  theme: $--color-primary;
}
