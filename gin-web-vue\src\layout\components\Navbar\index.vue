<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <breadcrumb
      id="breadcrumb-container"
      class="breadcrumb-container"
    />
    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <header-search class="right-menu-item" />
        <screenfull class="right-menu-item hover-effect" />
        <el-tooltip
          :content="$t('navbarBtn.size')"
          effect="dark"
          placement="bottom"
        >
          <size-select class="right-menu-item hover-effect" />
        </el-tooltip>
        <lang-select class="right-menu-item hover-effect" />
        <el-tooltip
          :content="$t('navbarBtn.message')"
          effect="dark"
          placement="bottom"
        >
          <message-center class="right-menu-item hover-effect" />
        </el-tooltip>
        <el-tooltip
          :content="$t('navbarBtn.loki')"
          effect="dark"
          placement="bottom"
        >
          <a
            class="right-menu-item hover-effect"
            target="_blank"
            :href="url + ':3000/'"
          >
            <i class="el-icon-document-copy" />
          </a>
        </el-tooltip>
        <el-tooltip
          :content="$t('navbarBtn.minio')"
          effect="dark"
          placement="bottom"
        >
          <a
            class="right-menu-item hover-effect"
            target="_blank"
            :href="url + ':9005/'"
          >
            <i class="el-icon-coin" />
          </a>
        </el-tooltip>
      </template>
      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <img
            :src="avatar+'?imageView2/1/w/80/h/80'"
            class="user-avatar"
          >
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/">
            <el-dropdown-item>
              {{ $t('dashboard') }}
            </el-dropdown-item>
          </router-link>
          <a
            target="_blank"
            href="https://piupuer.github.io/gin-web-slate"
          >
            <el-dropdown-item>{{ $t('navbarBtn.docs') }}</el-dropdown-item>
          </a>
          <a
            target="_blank"
            href="/swagger/index.html"
          >
            <el-dropdown-item>{{ $t('navbarBtn.swagger') }}</el-dropdown-item>
          </a>
          <router-link to="/profile/">
            <el-dropdown-item>
              {{ $t('navbarBtn.profile') }}
            </el-dropdown-item>
          </router-link>
          <a
            target="_blank"
            href="https://github.com/piupuer/gin-web"
          >
            <el-dropdown-item>
              {{ $t('navbarBtn.github') }}
            </el-dropdown-item>
          </a>
          <el-dropdown-item
            divided
            @click.native="logout"
          >
            <span style="display:block;">
              {{ $t('logout') }}
            </span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { AppModule } from '@/store/modules/app'
import { UserModule } from '@/store/modules/user'
import Breadcrumb from '@/components/Breadcrumb/index.vue'
import Hamburger from '@/components/Hamburger/index.vue'
import HeaderSearch from '@/components/HeaderSearch/index.vue'
import LangSelect from '@/components/LangSelect/index.vue'
import Screenfull from '@/components/Screenfull/index.vue'
import SizeSelect from '@/components/SizeSelect/index.vue'
import MessageCenter from '@/components/MessageCenter/index.vue'

@Component({
  name: 'Navbar',
  components: {
    Breadcrumb,
    Hamburger,
    HeaderSearch,
    LangSelect,
    Screenfull,
    SizeSelect,
    MessageCenter
  }
})
export default class extends Vue {
  get url() {
    return location.protocol + '//' + location.hostname
  }

  get sidebar() {
    return AppModule.sidebar
  }

  get device() {
    return AppModule.device.toString()
  }

  get avatar() {
    return UserModule.avatar
  }

  private toggleSideBar() {
    AppModule.ToggleSideBar(false)
  }

  private async logout() {
    await UserModule.LogOut()
    this.$router.push(`/login?redirect=${this.$route.fullPath}`)
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    padding: 0 15px;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-left: 30px;
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
