<h1 align="center">Gin Web Vue</h1>

<div align="center">
由gin + gorm + jwt + casbin组合实现的RBAC权限管理脚手架前端Vue Typescript版本, 搭建完成即可快速、高效投入业务开发
<p align="center">
<img src="https://img.shields.io/badge/Vue-2.6.11-brightgreen" alt="Vue version"/>
<img src="https://img.shields.io/badge/ElementUI-2.13.0-brightgreen" alt="Element-ui version"/>
<img src="https://img.shields.io/github/license/piupuer/gin-web-vue" alt="License"/>
</p>
</div>

## 默认菜单

- 首页
- 系统管理
  - 菜单管理
  - 角色管理
  - 用户管理
  - 接口管理
  - 数据字典
  - 操作日志
  - 消息推送
  - 机器管理
- 状态机
  - 状态机配置
  - 我的请假条
  - 待审批列表
- 上传组件
  - 上传示例1
  - 上传示例2(主要是针对ZIP压缩包上传及解压)
- 测试页面
  - 测试用例

## 在线演示(前端静态文件已上传至阿里云OSS, 访问速度飞起)

[传送门](http://**************/)


## 快速开始

```
git clone https://github.com/piupuer/gin-web-vue
cd gin-web-vue
# 安装依赖文件, 可以使用cnpm镜像加速
# npm install -g cnpm --registry=https://registry.npm.taobao.org
# cnpm install
npm install
# 本地调试
npm run serve
# 正式发布
npm run build:prod
```

> 启动成功之后, 可在浏览器中输入: [http://127.0.0.1:10001](http://127.0.0.1:10001), 若不能访问请检查node依赖是否安装成功

## 后端

- 项目地址: [gin-web](https://github.com/piupuer/gin-web)
- 实现方式: Golang

## 特别感谢

[Element UI](https://github.com/ElemeFE/element): A Vue.js 2.0 UI Toolkit for Web.
<br/>
[vue-element-admin](https://github.com/PanJiaChen/vue-element-admin): a production-ready front-end solution for admin interfaces.
<br/>
[vue-typescript-admin-template](https://github.com/Armour/vue-typescript-admin-template): a production-ready front-end solution for admin interfaces based on vue, typescript and UI Toolkit element-ui.
<br/>

## 互动交流

### QQ群：943724601

<img src="https://github.com/piupuer/gin-web-images/blob/master/contact/qq_group.jpeg?raw=true" width="256" alt="QQ群" />

## MIT License

    Copyright (c) 2020 piupuer
