# vue-svgicon

## English

* All svg components were generated by `vue-svgicon` using svg files
* After you adding new svg files into `icons/svg` folder, run `yarn svg` to regerenrate all svg components (before this, you should have `vue-svgicon` installed globally or use `npx`)
* See details at: [https://github.com/MMF-FE/vue-svgicon](https://github.com/MMF-FE/vue-svgicon)

## 中文

* 所有的 svg 组件都是由 `vue-svgicon` 生成的
* 每当在 `icons/svg` 文件夹内添加 icon 之后，可以通过执行 `yarn svg` 来重新生成所有组件 (在此之前需要全局安装 `vue-svgicon` 或使用 `npx`)
* 详细文档请见：[https://github.com/MMF-FE/vue-svgicon](https://github.com/MMF-FE/vue-svgicon)
