// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path')
// eslint-disable-next-line @typescript-eslint/no-var-requires
const WebpackAliossPlugin = require('webpack-alioss-plugin')

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, on Mac: sudo npm run / sudo yarn
const devServerPort = 10001 // TODO: get this variable from setting.ts
const name = 'Gin Web Vue' // TODO: get this variable from setting.ts

// 配置文件上传到阿里云CDN
// export WEBPACK_ALIOSS_OPEN=true && \
//   export WEBPACK_ALIOSS_PLUGIN_ACCESS_KEY_ID='' && \
//   export WEBPACK_ALIOSS_PLUGIN_ACCESS_KEY_SECRET='' && \
//   export WEBPACK_ALIOSS_PLUGIN_BUCKET='' && \
//   export WEBPACK_ALIOSS_PLUGIN_REGION='' && \
//   export WEBPACK_ALIOSS_PLUGIN_OSS_BASE_DIR=go
let publicPath = './'
let ossUri = ''
let plugins = []

if (process.env.NODE_ENV === 'production' && process.env.WEBPACK_ALIOSS_OPEN === 'true') {
  publicPath = `//${process.env.WEBPACK_ALIOSS_PLUGIN_BUCKET}.${process.env.WEBPACK_ALIOSS_PLUGIN_REGION}.aliyuncs.com/go/gin-web-vue/`
  plugins = [new WebpackAliossPlugin()]
  ossUri = publicPath
}

module.exports = {
  // 使用./替换/可兼容二级域名
  publicPath: publicPath,
  lintOnSave: process.env.NODE_ENV === 'development',
  configureWebpack: {
    plugins
  },
  productionSourceMap: false,
  // 某些包用的ES6以上语法, 需要在此处转译作兼容处理(IE11以下白屏 / IOS 11.2移动端白屏)
  transpileDependencies: [
    'fuse.js'
  ],
  devServer: {
    // 不检测host
    disableHostCheck: true,
    port: devServerPort,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    progress: false
  },
  pwa: {
    name: name,
    workboxPluginMode: 'InjectManifest',
    workboxOptions: {
      swSrc: path.resolve(__dirname, 'src/pwa/service-worker.js')
    }
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [
        path.resolve(__dirname, 'src/styles/_variables.scss'),
        path.resolve(__dirname, 'src/styles/_mixins.scss')
      ]
    }
  },
  chainWebpack(config) {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    config.set('name', name)

    // https://webpack.js.org/configuration/devtool/#development
    config
      .when(process.env.NODE_ENV === 'development',
        config => config.devtool('cheap-eval-source-map')
      )

    // remove vue-cli-service's progress output
    config.plugins.delete('progress')
    // replace with another progress output plugin to solve the this bug:
    // https://github.com/vuejs/vue-cli/issues/4557
    config.plugin('simple-progress-webpack-plugin')
      .use(require.resolve('simple-progress-webpack-plugin'), [{
        format: 'compact'
      }])

    config.plugin('define').tap(args => {
      Object.assign(args[0]['process.env'], {
        OSS_URI: JSON.stringify(ossUri)
      })
      return args
    })

    config
      .when(process.env.NODE_ENV !== 'development',
        config => {
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial' // only package third parties that are initially dependent
                },
                elementUI: {
                  name: 'chunk-elementUI', // split elementUI into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
                },
                commons: {
                  name: 'chunk-commons',
                  test: path.resolve(__dirname, 'src/components'),
                  minChunks: 3, //  minimum common number
                  priority: 5,
                  reuseExistingChunk: true
                }
              }
            })
          config.optimization.runtimeChunk('single')
        }
      )
  }
}
