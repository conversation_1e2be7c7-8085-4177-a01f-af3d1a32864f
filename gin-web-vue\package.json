{"name": "gin-web-vue", "version": "1.0.0", "private": true, "author": "piupuer <<EMAIL>>", "scripts": {"serve": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "lint": "vue-cli-service lint", "svg": "vsvg -s ./src/icons/svg -t ./src/icons/components --ext ts --es6"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@tinymce/tinymce-vue": "^3.2.0", "@types/crypto-js": "^4.1.0", "axios": "^0.19.2", "clipboard": "^2.0.6", "codemirror": "^5.52.2", "core-js": "^3.6.4", "cors": "^2.8.5", "crypto-js": "^4.1.1", "driver.js": "^0.9.8", "echarts": "^4.7.0", "element-ui": "^2.13.0", "faker": "^4.1.0", "file-saver": "^2.0.2", "fuse.js": "^5.1.0", "js-cookie": "^2.2.1", "jsencrypt": "^3.1.0", "json-stable-stringify": "^1.0.1", "jsondiffpatch": "^0.4.1", "jsonlint": "^1.6.3", "jszip": "^3.3.0", "lodash": "^4.17.15", "morgan": "^1.10.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.1.0", "register-service-worker": "^1.7.1", "screenfull": "^5.0.2", "script-loader": "^0.7.2", "simple-progress-webpack-plugin": "^1.1.2", "sortablejs": "^1.10.2", "tinymce": "^5.2.1", "vue": "^2.6.11", "vue-class-component": "^7.2.3", "vue-count-to": "^1.0.13", "vue-i18n": "^8.16.0", "vue-image-crop-upload": "^2.5.0", "vue-json-views": "^1.3.0", "vue-property-decorator": "^8.4.1", "vue-router": "^3.1.6", "vue-simple-uploader": "^0.7.4", "vue-splitpane": "^1.0.6", "vue-svgicon": "^3.2.6", "vue2-dropzone": "^3.6.0", "vuedraggable": "^2.23.2", "vuex": "^3.1.3", "vuex-module-decorators": "^0.16.1", "xlsx": "^0.15.6", "xterm": "^4.9.0", "xterm-addon-attach": "^0.6.0", "xterm-addon-fit": "^0.4.0", "xterm-addon-search": "^0.7.0", "xterm-addon-web-links": "^0.4.0", "yamljs": "^0.3.0"}, "devDependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@types/clipboard": "^2.0.1", "@types/codemirror": "^0.0.90", "@types/compression": "^1.7.0", "@types/cors": "^2.8.6", "@types/echarts": "^4.4.4", "@types/express": "^4.17.4", "@types/faker": "^4.1.11", "@types/file-saver": "^2.0.1", "@types/jest": "^25.1.5", "@types/js-cookie": "^2.2.5", "@types/json-stable-stringify": "^1.0.33", "@types/jszip": "^3.1.7", "@types/lodash": "^4.14.149", "@types/morgan": "^1.9.0", "@types/node": "^13.11.0", "@types/nprogress": "^0.2.0", "@types/sortablejs": "^1.10.4", "@types/tinymce": "^4.5.24", "@types/webpack-env": "^1.15.1", "@types/yamljs": "^0.2.30", "@typescript-eslint/eslint-plugin": "^2.26.0", "@typescript-eslint/parser": "^2.26.0", "@vue/cli-plugin-babel": "^4.2.3", "@vue/cli-plugin-eslint": "^4.2.3", "@vue/cli-plugin-pwa": "^4.2.3", "@vue/cli-plugin-router": "^4.2.3", "@vue/cli-plugin-typescript": "^4.2.3", "@vue/cli-plugin-unit-jest": "^4.2.3", "@vue/cli-plugin-vuex": "^4.2.3", "@vue/cli-service": "^4.2.3", "@vue/eslint-config-standard": "^5.1.2", "@vue/eslint-config-typescript": "^5.0.2", "@vue/test-utils": "^1.0.0-beta.32", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.1.0", "concurrently": "^5.1.0", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^6.2.2", "fibers": "^4.0.2", "jest": "^25.2.7", "lint-staged": "^10.1.1", "sass": "^1.26.3", "sass-loader": "^8.0.2", "style-resources-loader": "^1.3.3", "swagger-routes-express": "^3.1.2", "ts-jest": "^25.3.0", "ts-node-dev": "^1.0.0-pre.44", "typescript": "^3.8.3", "vue-cli-plugin-element": "^1.0.1", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-template-compiler": "^2.6.11", "webpack": "^4.42.1", "webpack-alioss-plugin": "^2.4.0"}, "bugs": {"url": "https://github.com/piupuer/gin-web-vue/issues"}, "gitHooks": {"pre-commit": "lint-staged"}, "keywords": ["vue", "typescript", "admin", "template", "element-ui"], "lint-staged": {"*.{js,vue}": ["vue-cli-service lint"]}, "repository": {"type": "git", "url": "git+https://github.com/piupuer/gin-web-vue.git"}}