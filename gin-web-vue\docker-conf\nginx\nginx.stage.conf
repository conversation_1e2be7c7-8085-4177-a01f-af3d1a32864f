#user  root;
worker_processes  1;

#error_log  /var/log/nginx/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log;
    error_log  /var/log/nginx/error.log;
    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    gzip  on;
    gzip_min_length 20;
    gzip_buffers 4 8k;
    gzip_types application/json;

    upstream stage-api {
        # 这里的gin-web是docker的内网网关, 参见gin-web-docker项目docker-compose.yml配置
        server docker-web:9090;
        keepalive 64;
    }

    server {
        listen 9091;
        listen [::]:9091;


        # Add index.php to the list if you are using PHP
        index index.html index.htm index.nginx-debian.html;

        # server_name localhost;
        # 这里可以改成部署机器IP或自定义域名
        server_name piupuer-local.com;

        location / {
           root /usr/share/nginx/html;
        }


        location ^~ /stage-api {
           proxy_redirect     off;
           proxy_set_header   X-Real-IP            $remote_addr;
           proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
           proxy_set_header   X-Forwarded-Proto $scheme;
           proxy_set_header   Host                   $http_host;
           proxy_set_header   X-NginX-Proxy    true;
           proxy_set_header   Connection "";
           proxy_http_version 1.1;
           proxy_pass         http://stage-api;
        }

        location ^~ /swagger {
           proxy_redirect     off;
           proxy_set_header   X-Real-IP            $remote_addr;
           proxy_set_header   X-Forwarded-For  $proxy_add_x_forwarded_for;
           proxy_set_header   X-Forwarded-Proto $scheme;
           proxy_set_header   Host                   $http_host;
           proxy_set_header   X-NginX-Proxy    true;
           proxy_set_header   Connection "";
           proxy_http_version 1.1;
           proxy_pass         http://swagger;
        }
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}
    include servers/*;
}
