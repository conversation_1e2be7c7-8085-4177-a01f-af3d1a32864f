// Package swagger GENERATED BY THE COMMAND ABOVE; DO NOT EDIT
// This file was generated by swaggo/swag
package swagger

import (
	"bytes"
	"encoding/json"
	"strings"
	"text/template"

	"github.com/swaggo/swag"
)

var doc = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "license": {
            "name": "MIT",
            "url": "https://github.com/piupuer/gin-web/blob/dev/LICENSE"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/all/category/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindApiGroupByCategoryByRoleKeyword",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Api"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "method",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/api/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateApi",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Api"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.CreateApi"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/api/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteApiByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Api"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/api/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindApi",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Api"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "method",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/api/role/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateApiByRoleId",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Api"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.UpdateMenuIncrementalIds"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/api/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateApiById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Api"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.UpdateApi"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/base/captcha": {
            "get": {
                "description": "GetCaptcha",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Base"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/base/idempotenceToken": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "IdempotenceToken",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Base"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/base/login": {
            "post": {
                "description": "Login",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Base"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.LoginCheck"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/base/logout": {
            "post": {
                "description": "Logout",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Base"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/base/refreshToken": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "RefreshToken",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Base"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/base/user/reset": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "ResetUserPwd",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Base"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.ResetUserPwd"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/base/user/status": {
            "post": {
                "description": "GetUserStatus",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Base"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "name": "lockExpire",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "locked",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "wrong",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/delay/export/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteDelayExportByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Delay"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/delay/export/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindDelayExport",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Delay"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "end",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/dict/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateDict",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Dict"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.CreateDict"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/dict/data/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateDictData",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Dict"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.CreateDictData"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/dict/data/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteDictDataByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Dict"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/dict/data/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindDictData",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Dict"
                ],
                "parameters": [
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "dictId",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "key",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "val",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/dict/data/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateDictDataById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Dict"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.UpdateDictData"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/dict/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteDictByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Dict"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/dict/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindDict",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Dict"
                ],
                "parameters": [
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "desc",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/dict/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateDictById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Dict"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.UpdateDict"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateFsm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.FsmCreateMachine"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteFsmByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindFsm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "submitterConfirm",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "submitterName",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/log/approve": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FsmApproveLog",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "name": "approvalOpinion",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "approvalRoleId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "approvalUserId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "approved",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "uuid",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/log/approving/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindFsmApprovingLog",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "name": "approvalRoleId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "approvalUserId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/log/cancel": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FsmCancelLogByUuids",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "name": "approvalRoleId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "approvalUserId",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "name": "uuids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/log/submitter/detail": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "GetFsmLogSubmitterDetail",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "uuid",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            },
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateFsmLogSubmitterDetail",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.UpdateFsmLogSubmitterDetail"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/log/track": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindFsmLogTrack",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "name": "category",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "uuid",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/fsm/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateFsmById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Fsm"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.FsmUpdateMachine"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/leave/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateLeave",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Leave"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.CreateLeave"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/leave/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteLeaveByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Leave"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/leave/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindLeave",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Leave"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "name": "approvalOpinion",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "desc",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/leave/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateLeaveById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Leave"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.UpdateLeave"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/machine/connect/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "ConnectMachineById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Machine"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/machine/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateMachine",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Machine"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.CreateMachine"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/machine/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteMachineByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Machine"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/machine/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindMachine",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Machine"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "name": "arch",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "cpu",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "disk",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "host",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "loginName",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "loginPwd",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "memory",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "remark",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "sshPort",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "version",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/machine/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateMachineById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Machine"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.UpdateMachine"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/menu/all/{id}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindMenuByRoleId",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Menu"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/menu/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateMenu",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Menu"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.CreateMenu"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/menu/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteMenuByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Menu"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/menu/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindMenu",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Menu"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "name": "breadcrumb",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "component",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "redirect",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "title",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "visible",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/menu/role/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateMenuByRoleId",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Menu"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.UpdateMenuIncrementalIds"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/menu/tree": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "GetMenuTree",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Menu"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/menu/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateMenuById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Menu"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.UpdateMenu"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/message/deleted/all": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateAllMessageDeleted",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Message"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/message/deleted/batch": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchUpdateMessageDeleted",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Message"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "description": "id array string, split by comma",
                        "name": "ids",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/message/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindMessage",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Message"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "name": "content",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "title",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "toUserId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "type",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/message/read/all": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateAllMessageRead",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Message"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/message/read/batch": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchUpdateMessageRead",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Message"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/message/unRead/count": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "GetUnReadMessageCount",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Message"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "PushMessage",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Message"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.PushMessage"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/operation/log/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteOperationLogByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*OperationLog"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/operation/log/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindOperationLog",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*OperationLog"
                ],
                "parameters": [
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "ip",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "method",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "path",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "username",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/role/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateRole",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Role"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.CreateRole"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/role/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteRoleByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Role"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/role/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindRole",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Role"
                ],
                "parameters": [
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "currentRoleSort",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "keyword",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/role/list/{ids}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindRoleByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Role"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "description": "ids",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/role/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateRoleById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Role"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.UpdateRole"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/upload/file": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UploadFileChunkExists",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Upload"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "name": "chunkNumber",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "chunkSize",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "whether transfer complete",
                        "name": "complete",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "filename",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "identifier",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "totalSize",
                        "in": "query"
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "integer"
                        },
                        "description": "uploaded block numbers",
                        "name": "uploaded",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UploadFile",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Upload"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.FilePartInfo"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/upload/merge": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UploadMerge",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Upload"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.FilePartInfo"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/upload/unzip": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UploadUnZip",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "*Upload"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.FilePartInfo"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/user/changePwd": {
            "put": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "ChangePwd",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.ChangePwd"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/user/create": {
            "post": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "CreateUser",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "parameters": [
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.CreateUser"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/user/delete/batch": {
            "delete": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "BatchDeleteUserByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "parameters": [
                    {
                        "description": "ids",
                        "name": "ids",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/req.Ids"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/user/info": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "GetUserInfo",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/user/list": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindUser",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "name": "avatar",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "use count cache",
                        "name": "countCache",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "introduction",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "mobile",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "mobileOr",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "nickname",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "nicknameOr",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "query all data",
                        "name": "noPagination",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "current page",
                        "name": "pageNum",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "page per count",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "roleId",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "not use 'SELECT count(*) FROM ...' before 'SELECT * FROM ...'",
                        "name": "skipCount",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "all data count",
                        "name": "total",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "username",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "usernameOr",
                        "in": "query"
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/user/list/{ids}": {
            "get": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "FindUserByIds",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "parameters": [
                    {
                        "type": "string",
                        "description": "ids",
                        "name": "ids",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        },
        "/user/update/{id}": {
            "patch": {
                "security": [
                    {
                        "Bearer": []
                    }
                ],
                "description": "UpdateUserById",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User"
                ],
                "parameters": [
                    {
                        "type": "integer",
                        "description": "id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "params",
                        "name": "params",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/request.UpdateUser"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "success",
                        "schema": {
                            "$ref": "#/definitions/resp.Resp"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "req.CreateApi": {
            "type": "object",
            "required": [
                "category",
                "method",
                "path"
            ],
            "properties": {
                "category": {
                    "type": "string"
                },
                "desc": {
                    "type": "string"
                },
                "method": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "roleIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "roleKeywords": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "req.CreateDict": {
            "type": "object",
            "required": [
                "desc",
                "name"
            ],
            "properties": {
                "desc": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                }
            }
        },
        "req.CreateDictData": {
            "type": "object",
            "required": [
                "dictId",
                "key",
                "val"
            ],
            "properties": {
                "addition": {
                    "type": "string"
                },
                "dictId": {
                    "type": "integer"
                },
                "key": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "val": {
                    "type": "string"
                }
            }
        },
        "req.CreateMachine": {
            "type": "object",
            "required": [
                "host",
                "loginName",
                "loginPwd",
                "sshPort"
            ],
            "properties": {
                "arch": {
                    "type": "string"
                },
                "cpu": {
                    "type": "string"
                },
                "disk": {
                    "type": "string"
                },
                "host": {
                    "type": "string"
                },
                "loginName": {
                    "type": "string"
                },
                "loginPwd": {
                    "type": "string"
                },
                "memory": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "sshPort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "req.CreateMenu": {
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "breadcrumb": {
                    "type": "integer"
                },
                "component": {
                    "type": "string"
                },
                "icon": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "parentId": {
                    "type": "integer"
                },
                "path": {
                    "type": "string"
                },
                "permission": {
                    "type": "string"
                },
                "redirect": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "visible": {
                    "type": "integer"
                }
            }
        },
        "req.FilePartInfo": {
            "type": "object",
            "properties": {
                "chunkNumber": {
                    "type": "integer"
                },
                "chunkSize": {
                    "type": "integer"
                },
                "complete": {
                    "description": "whether transfer complete",
                    "type": "boolean"
                },
                "filename": {
                    "type": "string"
                },
                "identifier": {
                    "type": "string"
                },
                "totalSize": {
                    "type": "integer"
                },
                "uploaded": {
                    "description": "uploaded block numbers",
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "req.FsmCreateEvent": {
            "type": "object",
            "properties": {
                "edit": {
                    "type": "integer"
                },
                "editFields": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "roles": {
                    "type": "string"
                },
                "users": {
                    "type": "string"
                }
            }
        },
        "req.FsmCreateMachine": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "integer"
                },
                "levels": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/req.FsmCreateEvent"
                    }
                },
                "name": {
                    "type": "string"
                },
                "submitterConfirm": {
                    "type": "integer"
                },
                "submitterConfirmEditFields": {
                    "type": "string"
                },
                "submitterEditFields": {
                    "type": "string"
                },
                "submitterName": {
                    "type": "string"
                }
            }
        },
        "req.FsmSubmitterDetailField": {
            "type": "object",
            "properties": {
                "key": {
                    "type": "string"
                },
                "val": {
                    "type": "string"
                }
            }
        },
        "req.FsmUpdateMachine": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "integer"
                },
                "levels": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/req.FsmCreateEvent"
                    }
                },
                "name": {
                    "type": "string"
                },
                "submitterConfirm": {
                    "type": "integer"
                },
                "submitterConfirmEditFields": {
                    "type": "string"
                },
                "submitterEditFields": {
                    "type": "string"
                },
                "submitterName": {
                    "type": "string"
                }
            }
        },
        "req.Ids": {
            "type": "object",
            "properties": {
                "ids": {
                    "description": "id array string, split by comma",
                    "type": "string"
                }
            }
        },
        "req.LoginCheck": {
            "type": "object",
            "properties": {
                "captchaAnswer": {
                    "type": "string"
                },
                "captchaId": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "req.PushMessage": {
            "type": "object",
            "required": [
                "content",
                "title",
                "type"
            ],
            "properties": {
                "content": {
                    "type": "string"
                },
                "fromUserId": {
                    "type": "integer"
                },
                "idempotenceToken": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "toRoleIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "toUserIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "type": {
                    "type": "integer"
                }
            }
        },
        "req.ResetUserPwd": {
            "type": "object",
            "properties": {
                "newPassword": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "req.UpdateApi": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "string"
                },
                "desc": {
                    "type": "string"
                },
                "method": {
                    "type": "string"
                },
                "path": {
                    "type": "string"
                },
                "roleIds": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "title": {
                    "type": "string"
                }
            }
        },
        "req.UpdateDict": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "status": {
                    "type": "integer"
                }
            }
        },
        "req.UpdateDictData": {
            "type": "object",
            "properties": {
                "addition": {
                    "type": "string"
                },
                "dictId": {
                    "type": "integer"
                },
                "key": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "val": {
                    "type": "string"
                }
            }
        },
        "req.UpdateFsmLogSubmitterDetail": {
            "type": "object",
            "properties": {
                "category": {
                    "type": "integer"
                },
                "fields": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/req.FsmSubmitterDetailField"
                    }
                },
                "uuid": {
                    "type": "string"
                }
            }
        },
        "req.UpdateMachine": {
            "type": "object",
            "properties": {
                "arch": {
                    "type": "string"
                },
                "cpu": {
                    "type": "string"
                },
                "disk": {
                    "type": "string"
                },
                "host": {
                    "type": "string"
                },
                "loginName": {
                    "type": "string"
                },
                "loginPwd": {
                    "type": "string"
                },
                "memory": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "remark": {
                    "type": "string"
                },
                "sshPort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "req.UpdateMenu": {
            "type": "object",
            "properties": {
                "breadcrumb": {
                    "type": "integer"
                },
                "component": {
                    "type": "string"
                },
                "icon": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "parentId": {
                    "type": "integer"
                },
                "path": {
                    "type": "string"
                },
                "permission": {
                    "type": "string"
                },
                "redirect": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "title": {
                    "type": "string"
                },
                "visible": {
                    "type": "integer"
                }
            }
        },
        "req.UpdateMenuIncrementalIds": {
            "type": "object",
            "properties": {
                "create": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "delete": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "request.ChangePwd": {
            "type": "object",
            "properties": {
                "newPassword": {
                    "type": "string"
                },
                "oldPassword": {
                    "type": "string"
                }
            }
        },
        "request.CreateLeave": {
            "type": "object",
            "required": [
                "desc"
            ],
            "properties": {
                "desc": {
                    "type": "string"
                },
                "endTime": {
                    "type": "string"
                },
                "startTime": {
                    "type": "string"
                }
            }
        },
        "request.CreateRole": {
            "type": "object",
            "required": [
                "keyword",
                "name",
                "sort"
            ],
            "properties": {
                "currentRoleSort": {
                    "type": "integer"
                },
                "desc": {
                    "type": "string"
                },
                "keyword": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                }
            }
        },
        "request.CreateUser": {
            "type": "object",
            "required": [
                "initPassword",
                "mobile",
                "roleId",
                "username"
            ],
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "initPassword": {
                    "type": "string"
                },
                "introduction": {
                    "type": "string"
                },
                "mobile": {
                    "type": "string"
                },
                "newPassword": {
                    "type": "string"
                },
                "nickname": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "roleId": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "request.UpdateLeave": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "endTime": {
                    "type": "string"
                },
                "startTime": {
                    "type": "string"
                }
            }
        },
        "request.UpdateRole": {
            "type": "object",
            "properties": {
                "desc": {
                    "type": "string"
                },
                "keyword": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "sort": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                }
            }
        },
        "request.UpdateUser": {
            "type": "object",
            "properties": {
                "avatar": {
                    "type": "string"
                },
                "initPassword": {
                    "type": "string"
                },
                "introduction": {
                    "type": "string"
                },
                "lockExpire": {
                    "type": "integer"
                },
                "locked": {
                    "type": "integer"
                },
                "mobile": {
                    "type": "string"
                },
                "newPassword": {
                    "type": "string"
                },
                "nickname": {
                    "type": "string"
                },
                "password": {
                    "type": "string"
                },
                "roleId": {
                    "type": "integer"
                },
                "status": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                },
                "wrong": {
                    "type": "integer"
                }
            }
        },
        "resp.Resp": {
            "type": "object",
            "properties": {
                "code": {
                    "description": "response code",
                    "type": "integer",
                    "enum": [
                        201,
                        401,
                        403,
                        405,
                        500
                    ]
                },
                "data": {
                    "description": "response data if code=201",
                    "type": "string",
                    "example": "{}"
                },
                "msg": {
                    "description": "response msg",
                    "type": "string",
                    "example": "success"
                },
                "requestId": {
                    "description": "request id",
                    "type": "string",
                    "example": "4cb6e3f6-1f52-4fba-9b7d-e65098600f02"
                }
            }
        }
    },
    "securityDefinitions": {
        "Bearer": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

type swaggerInfo struct {
	Version     string
	Host        string
	BasePath    string
	Schemes     []string
	Title       string
	Description string
}

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = swaggerInfo{
	Version:     "1.2.1",
	Host:        "",
	BasePath:    "",
	Schemes:     []string{},
	Title:       "Gin Web",
	Description: "A simple RBAC admin system written by golang",
}

type s struct{}

func (s *s) ReadDoc() string {
	sInfo := SwaggerInfo
	sInfo.Description = strings.Replace(sInfo.Description, "\n", "\\n", -1)

	t, err := template.New("swagger_info").Funcs(template.FuncMap{
		"marshal": func(v interface{}) string {
			a, _ := json.Marshal(v)
			return string(a)
		},
		"escape": func(v interface{}) string {
			// escape tabs
			str := strings.Replace(v.(string), "\t", "\\t", -1)
			// replace " with \", and if that results in \\", replace that with \\\"
			str = strings.Replace(str, "\"", "\\\"", -1)
			return strings.Replace(str, "\\\\\"", "\\\\\\\"", -1)
		},
	}).Parse(doc)
	if err != nil {
		return doc
	}

	var tpl bytes.Buffer
	if err := t.Execute(&tpl, sInfo); err != nil {
		return doc
	}

	return tpl.String()
}

func init() {
	swag.Register("swagger", &s{})
}
